import customtkinter as ctk
from tkinter import messagebox, filedialog, simpledialog
import threading
import requests
import json
import os
import sys
import hashlib
import winreg
from pathlib import Path
import time
from datetime import datetime
import shutil

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from keyauth import api

# Import configuration classes from original file
from steam_tools_gui import ConfigManager, PasswordManager

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class ModernSteamToolsGUI:
    def __init__(self):
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("Steam Tools Downloader v2")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize configuration and password managers
        self.config_manager = ConfigManager()
        self.password_manager = PasswordManager(self.config_manager)
        
        # Initialize variables
        self.steam_path = self.config_manager.get("steam_path", "")
        self.keyauth_app = None
        self.license_key = ""
        self.app_info = {}
        self.is_admin_mode = False
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-Shift-A>', self.toggle_admin_mode)
        self.root.bind('<Control-Shift-C>', self.switch_to_customer_mode)
        
        # Create GUI based on mode
        self.create_customer_mode()
        
        # Initialize Keyauth after GUI is created (in a separate thread to avoid blocking)
        threading.Thread(target=self.init_keyauth, daemon=True).start()
        
        # Auto-detect Steam path if not set
        if not self.steam_path:
            self.auto_detect_steam_path()
    
    def toggle_admin_mode(self, event=None):
        """Toggle between customer and admin mode with password verification"""
        if self.is_admin_mode:
            self.switch_to_customer_mode()
        else:
            self.switch_to_admin_mode()
    
    def switch_to_admin_mode(self):
        """Switch to admin mode with password verification"""
        password = simpledialog.askstring("Admin Access", "Enter admin password:", show='*')
        if password and self.password_manager.verify_password(password):
            self.is_admin_mode = True
            self.clear_widgets()
            self.create_admin_mode()
            self.log_message("Switched to Admin Mode")
        elif password:  # Password was entered but incorrect
            messagebox.showerror("Access Denied", "Incorrect admin password")
    
    def switch_to_customer_mode(self, event=None):
        """Switch to customer mode"""
        self.is_admin_mode = False
        self.clear_widgets()
        self.create_customer_mode()
        if hasattr(self, 'log_message'):
            self.log_message("Switched to Customer Mode")
    
    def clear_widgets(self):
        """Clear all widgets from the window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def create_customer_mode(self):
        """Create modern customer interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Header section
        header_frame = ctk.CTkFrame(main_frame, corner_radius=15, height=120)
        header_frame.pack(fill="x", pady=(0, 30))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = ctk.CTkLabel(header_frame, text="Steam Tools Downloader", 
                                  font=ctk.CTkFont(size=32, weight="bold"))
        title_label.pack(pady=(25, 5))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(header_frame, 
                                     text="Enter your license key to automatically download and install files",
                                     font=ctk.CTkFont(size=14),
                                     text_color=("gray60", "gray40"))
        subtitle_label.pack()
        
        # License Key Card
        license_card = ctk.CTkFrame(main_frame, corner_radius=15)
        license_card.pack(fill="x", pady=(0, 20))
        
        # License card content
        license_content = ctk.CTkFrame(license_card, fg_color="transparent")
        license_content.pack(fill="x", padx=30, pady=30)
        
        license_label = ctk.CTkLabel(license_content, text="License Key", 
                                    font=ctk.CTkFont(size=16, weight="bold"))
        license_label.pack(anchor="w", pady=(0, 10))
        
        self.license_entry = ctk.CTkEntry(license_content, height=45, 
                                         font=ctk.CTkFont(size=14),
                                         placeholder_text="Enter your license key here...")
        self.license_entry.pack(fill="x", pady=(0, 20))
        
        # Download button
        self.auto_download_btn = ctk.CTkButton(license_content, text="Download & Install", 
                                              height=50, font=ctk.CTkFont(size=16, weight="bold"),
                                              command=self.auto_download)
        self.auto_download_btn.pack(fill="x")
        
        # Progress Card
        progress_card = ctk.CTkFrame(main_frame, corner_radius=15)
        progress_card.pack(fill="x", pady=(0, 20))
        
        progress_content = ctk.CTkFrame(progress_card, fg_color="transparent")
        progress_content.pack(fill="x", padx=30, pady=30)
        
        progress_label = ctk.CTkLabel(progress_content, text="Progress", 
                                     font=ctk.CTkFont(size=16, weight="bold"))
        progress_label.pack(anchor="w", pady=(0, 10))
        
        self.progress_var = ctk.StringVar(value="Ready to download")
        self.progress_status = ctk.CTkLabel(progress_content, textvariable=self.progress_var,
                                           font=ctk.CTkFont(size=14))
        self.progress_status.pack(anchor="w", pady=(0, 15))
        
        self.progress_bar = ctk.CTkProgressBar(progress_content, height=8)
        self.progress_bar.pack(fill="x", pady=(0, 10))
        self.progress_bar.set(0)
        
        # Status Log Card
        log_card = ctk.CTkFrame(main_frame, corner_radius=15)
        log_card.pack(fill="both", expand=True)
        
        log_content = ctk.CTkFrame(log_card, fg_color="transparent")
        log_content.pack(fill="both", expand=True, padx=30, pady=30)
        
        log_label = ctk.CTkLabel(log_content, text="Status Log", 
                                font=ctk.CTkFont(size=16, weight="bold"))
        log_label.pack(anchor="w", pady=(0, 10))
        
        self.status_text = ctk.CTkTextbox(log_content, height=200, 
                                         font=ctk.CTkFont(family="Consolas", size=12))
        self.status_text.pack(fill="both", expand=True)
        
        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent", height=40)
        footer_frame.pack(fill="x", pady=(20, 0))
        footer_frame.pack_propagate(False)
        
        hint_label = ctk.CTkLabel(footer_frame, text="Press Ctrl+Shift+A for admin mode", 
                                 font=ctk.CTkFont(size=12),
                                 text_color=("gray60", "gray40"))
        hint_label.pack()
        
        # Set focus to license entry
        self.license_entry.focus()
        
        # Bind Enter key to auto-download
        self.license_entry.bind('<Return>', lambda e: self.auto_download())

    def create_admin_mode(self):
        """Create modern admin interface"""
        # Main container with two columns
        main_frame = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Header
        header_frame = ctk.CTkFrame(main_frame, corner_radius=15, height=100)
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = ctk.CTkLabel(header_frame, text="🔧 Steam Tools Admin Panel",
                                  font=ctk.CTkFont(size=28, weight="bold"))
        title_label.pack(pady=(20, 5))

        admin_indicator = ctk.CTkLabel(header_frame, text="Administrator Mode Active",
                                      font=ctk.CTkFont(size=14),
                                      text_color=("orange", "orange"))
        admin_indicator.pack()

        # Content area with two columns
        content_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True)
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)

        # Left column
        left_column = ctk.CTkFrame(content_frame, fg_color="transparent")
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        # Download Management Card
        download_card = ctk.CTkFrame(left_column, corner_radius=15)
        download_card.pack(fill="x", pady=(0, 15))

        download_content = ctk.CTkFrame(download_card, fg_color="transparent")
        download_content.pack(fill="x", padx=25, pady=25)

        ctk.CTkLabel(download_content, text="Download Management",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(anchor="w", pady=(0, 15))

        ctk.CTkLabel(download_content, text="License Key",
                    font=ctk.CTkFont(size=14)).pack(anchor="w", pady=(0, 5))

        self.license_entry = ctk.CTkEntry(download_content, height=40,
                                         font=ctk.CTkFont(size=13),
                                         placeholder_text="Enter license key...")
        self.license_entry.pack(fill="x", pady=(0, 15))

        self.download_btn = ctk.CTkButton(download_content, text="Download Files",
                                         height=45, font=ctk.CTkFont(size=14, weight="bold"),
                                         command=self.start_download)
        self.download_btn.pack(fill="x")

        # Steam Configuration Card
        steam_card = ctk.CTkFrame(left_column, corner_radius=15)
        steam_card.pack(fill="x", pady=(0, 15))

        steam_content = ctk.CTkFrame(steam_card, fg_color="transparent")
        steam_content.pack(fill="x", padx=25, pady=25)

        ctk.CTkLabel(steam_content, text="Steam Configuration",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(anchor="w", pady=(0, 15))

        ctk.CTkLabel(steam_content, text="Current Steam Path:",
                    font=ctk.CTkFont(size=14)).pack(anchor="w", pady=(0, 5))

        self.steam_path_var = ctk.StringVar(value=self.steam_path or "Not configured")
        path_label = ctk.CTkLabel(steam_content, textvariable=self.steam_path_var,
                                 font=ctk.CTkFont(size=12),
                                 text_color=("blue", "lightblue"))
        path_label.pack(anchor="w", pady=(0, 15))

        # Steam buttons
        steam_buttons = ctk.CTkFrame(steam_content, fg_color="transparent")
        steam_buttons.pack(fill="x")
        steam_buttons.grid_columnconfigure(0, weight=1)
        steam_buttons.grid_columnconfigure(1, weight=1)

        ctk.CTkButton(steam_buttons, text="Browse Path", height=35,
                     command=self.browse_steam_path).grid(row=0, column=0, sticky="ew", padx=(0, 5))
        ctk.CTkButton(steam_buttons, text="Auto-detect", height=35,
                     command=self.auto_detect_steam_path).grid(row=0, column=1, sticky="ew", padx=(5, 0))

        # Progress Card
        progress_card = ctk.CTkFrame(left_column, corner_radius=15)
        progress_card.pack(fill="x")

        progress_content = ctk.CTkFrame(progress_card, fg_color="transparent")
        progress_content.pack(fill="x", padx=25, pady=25)

        ctk.CTkLabel(progress_content, text="Progress",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(anchor="w", pady=(0, 15))

        self.progress_var = ctk.StringVar(value="Ready")
        self.progress_status = ctk.CTkLabel(progress_content, textvariable=self.progress_var,
                                           font=ctk.CTkFont(size=14))
        self.progress_status.pack(anchor="w", pady=(0, 10))

        self.progress_bar = ctk.CTkProgressBar(progress_content, height=8)
        self.progress_bar.pack(fill="x")
        self.progress_bar.set(0)

        # Right column
        right_column = ctk.CTkFrame(content_frame, fg_color="transparent")
        right_column.grid(row=0, column=1, sticky="nsew", padx=(10, 0))

        # Management Functions Card
        mgmt_card = ctk.CTkFrame(right_column, corner_radius=15)
        mgmt_card.pack(fill="x", pady=(0, 15))

        mgmt_content = ctk.CTkFrame(mgmt_card, fg_color="transparent")
        mgmt_content.pack(fill="x", padx=25, pady=25)

        ctk.CTkLabel(mgmt_content, text="Management Functions",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(anchor="w", pady=(0, 20))

        # Reset operations
        ctk.CTkLabel(mgmt_content, text="Reset Operations",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", pady=(0, 10))

        ctk.CTkButton(mgmt_content, text="Reset All Data", height=35,
                     command=self.reset_all_data,
                     fg_color=("red", "darkred")).pack(fill="x", pady=(0, 8))

        ctk.CTkButton(mgmt_content, text="Reset stplug-in Folder", height=35,
                     command=self.reset_stplug_folder,
                     fg_color=("orange", "darkorange")).pack(fill="x", pady=(0, 8))

        ctk.CTkButton(mgmt_content, text="Delete All Files", height=35,
                     command=self.delete_all_files,
                     fg_color=("red", "darkred")).pack(fill="x", pady=(0, 20))

        # Admin operations
        ctk.CTkLabel(mgmt_content, text="Admin Operations",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", pady=(0, 10))

        ctk.CTkButton(mgmt_content, text="View License History", height=35,
                     command=self.view_license_history).pack(fill="x", pady=(0, 8))

        ctk.CTkButton(mgmt_content, text="Change Password", height=35,
                     command=self.change_admin_password).pack(fill="x", pady=(0, 8))

        ctk.CTkButton(mgmt_content, text="Switch to Customer Mode", height=40,
                     command=self.switch_to_customer_mode,
                     font=ctk.CTkFont(size=14, weight="bold")).pack(fill="x")

        # Activity Log Card
        log_card = ctk.CTkFrame(right_column, corner_radius=15)
        log_card.pack(fill="both", expand=True)

        log_content = ctk.CTkFrame(log_card, fg_color="transparent")
        log_content.pack(fill="both", expand=True, padx=25, pady=25)

        ctk.CTkLabel(log_content, text="Activity Log",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(anchor="w", pady=(0, 15))

        self.log_text = ctk.CTkTextbox(log_content, height=300,
                                      font=ctk.CTkFont(family="Consolas", size=11))
        self.log_text.pack(fill="both", expand=True)

    def log_message(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}\n"

        # Use appropriate text widget based on mode
        try:
            if hasattr(self, 'status_text') and self.status_text.winfo_exists():  # Customer mode
                self.status_text.insert("end", log_text)
                self.status_text.see("end")
                self.root.update_idletasks()
            elif hasattr(self, 'log_text') and self.log_text.winfo_exists():  # Admin mode
                self.log_text.insert("end", log_text)
                self.log_text.see("end")
                self.root.update_idletasks()
            else:
                # Fallback: print to console if GUI not ready
                print(log_text.strip())
        except:
            # Fallback: print to console if any error
            print(log_text.strip())

    def auto_download(self):
        """Automatically validate license and download files"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return

        # Disable button during process
        self.auto_download_btn.configure(state='disabled')
        self.progress_bar.set(0)
        self.progress_var.set("Validating license...")

        # Clear status
        self.status_text.delete("1.0", "end")

        # Run in separate thread
        threading.Thread(target=self._auto_download_thread, args=(license_key,), daemon=True).start()

    def _auto_download_thread(self, license_key):
        """Auto-download thread"""
        try:
            # Validate license
            self.root.after(0, lambda: self.log_message("Validating license key..."))

            if not self.keyauth_app:
                self.root.after(0, lambda: self.log_message("❌ Keyauth not initialized"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Keyauth not initialized. Please wait and try again."))
                return

            is_valid = self.keyauth_app.license(license_key)

            if not is_valid:
                self.root.after(0, lambda: self.log_message("❌ Invalid license key"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Invalid license key"))
                return

            self.root.after(0, lambda: self.log_message("✅ License key validated"))

            # Get app information
            main_data = self.keyauth_app.var("Main")
            if not main_data:
                self.root.after(0, lambda: self.log_message("❌ Failed to get app data"))
                return

            apps_data = json.loads(main_data)
            app_info = None

            # Find matching app
            for app in apps_data["apps"]:
                if license_key.startswith(app["license_prefix"]):
                    app_info = app
                    break

            if not app_info:
                self.root.after(0, lambda: self.log_message("❌ No matching app found"))
                return

            self.root.after(0, lambda: self.log_message(f"📱 Found app: {app_info['app_name']}"))

            # Ensure Steam path is available
            if not self.steam_path:
                self.root.after(0, lambda: self.auto_detect_steam_path())
                if not self.steam_path:
                    self.root.after(0, lambda: self.log_message("❌ Steam path not found"))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Steam installation not found. Please contact admin."))
                    return

            # Start download process
            self.root.after(0, lambda: self.progress_var.set("Downloading files..."))
            success = self._download_all_files(app_info)

            # Add to history
            self.config_manager.add_license_history(license_key, app_info, success)

            if success:
                self.root.after(0, lambda: self.progress_var.set("✅ Installation completed!"))
                self.root.after(0, lambda: self.log_message("🎉 All files downloaded and installed successfully!"))
                self.root.after(0, lambda: messagebox.showinfo("Success", "Files downloaded and installed successfully!"))
            else:
                self.root.after(0, lambda: self.progress_var.set("❌ Installation failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Some files failed to download. Please try again."))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Error: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"An error occurred: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.auto_download_btn.configure(state='normal'))

    def init_keyauth(self):
        """Initialize Keyauth API"""
        try:
            self.keyauth_app = api(
                name="MainSteam",
                ownerid="1tGVnUKtzH",
                secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                version="1.0",
                hash_to_check=self.get_checksum()
            )
            self.log_message("Keyauth initialized successfully")
        except Exception as e:
            self.log_message(f"Failed to initialize Keyauth: {str(e)}")
            self.log_message("Please check your internet connection and try again.")

    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""

    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path from registry"""
        try:
            # Try to get Steam path from registry
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")
                    if hasattr(self, 'steam_path_var'):
                        self.steam_path_var.set(steam_path)
                    return True
        except:
            pass

        # Try alternative registry location
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "SteamPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")
                    if hasattr(self, 'steam_path_var'):
                        self.steam_path_var.set(steam_path)
                    return True
        except:
            pass

        # Try common installation paths
        common_paths = [
            r"C:\Program Files (x86)\Steam",
            r"C:\Program Files\Steam",
            r"D:\Steam",
            r"E:\Steam"
        ]

        for path in common_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "steam.exe")):
                self.steam_path = path
                self.config_manager.set("steam_path", path)
                self.log_message(f"Steam path found: {path}")
                if hasattr(self, 'steam_path_var'):
                    self.steam_path_var.set(path)
                return True

        self.log_message("Steam installation not found automatically")
        return False

    def _download_all_files(self, app_info):
        """Download all required files for the app"""
        try:
            total_files = 4
            current_file = 0
            success_count = 0

            # Create stplug-in directory
            stplug_in_path = os.path.join(self.steam_path, "config", "stplug-in")
            os.makedirs(stplug_in_path, exist_ok=True)
            self.log_message(f"📁 Created/verified directory: {stplug_in_path}")

            # Download hid.dll to Steam root
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading hid.dll ({current_file}/{total_files})"))
            steamtools_url = self.keyauth_app.var("SteamTools")
            if steamtools_url:
                hid_path = os.path.join(self.steam_path, "hid.dll")
                if self.download_file(steamtools_url, hid_path):
                    self.log_message("✅ hid.dll downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download hid.dll")

            self.root.after(0, lambda: self.progress_bar.set((current_file/total_files)))

            # Download SteamTools-File1 (luapacka.exe)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading luapacka.exe ({current_file}/{total_files})"))
            file1_url = self.keyauth_app.var("SteamTools-File1")
            if file1_url:
                file1_path = os.path.join(stplug_in_path, "luapacka.exe")
                if self.download_file(file1_url, file1_path):
                    self.log_message("✅ luapacka.exe downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download luapacka.exe")

            self.root.after(0, lambda: self.progress_bar.set((current_file/total_files)))

            # Download SteamTools-File2 (Steamtools.st)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading Steamtools.st ({current_file}/{total_files})"))
            file2_url = self.keyauth_app.var("SteamTools-File2")
            if file2_url:
                file2_path = os.path.join(stplug_in_path, "Steamtools.st")
                if self.download_file(file2_url, file2_path):
                    self.log_message("✅ Steamtools.st downloaded successfully")
                    success_count += 1
                else:
                    self.log_message("❌ Failed to download Steamtools.st")

            self.root.after(0, lambda: self.progress_bar.set((current_file/total_files)))

            # Download app-specific lua file
            current_file += 1
            app_name = app_info['app_name']
            self.root.after(0, lambda: self.progress_var.set(f"Downloading {app_name}.lua ({current_file}/{total_files})"))
            app_url = self.keyauth_app.var(app_name)
            if app_url:
                lua_filename = f"{app_info['app_id']}.lua"
                lua_path = os.path.join(stplug_in_path, lua_filename)
                if self.download_file(app_url, lua_path):
                    self.log_message(f"✅ {lua_filename} downloaded successfully")
                    success_count += 1
                else:
                    self.log_message(f"❌ Failed to download {lua_filename}")

            self.root.after(0, lambda: self.progress_bar.set(1.0))

            return success_count == total_files

        except Exception as e:
            self.log_message(f"❌ Download error: {str(e)}")
            return False

    def download_file(self, url, file_path):
        """Download a file from URL to local path"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            return True
        except Exception as e:
            self.log_message(f"❌ Download error for {os.path.basename(file_path)}: {str(e)}")
            return False

    def start_download(self):
        """Start download process (admin mode)"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return

        if not self.steam_path:
            messagebox.showerror("Error", "Please set Steam path first")
            return

        if not self.keyauth_app:
            messagebox.showerror("Error", "Keyauth not initialized. Please wait and try again.")
            return

        # Validate license
        try:
            is_valid = self.keyauth_app.license(license_key)
            if not is_valid:
                messagebox.showerror("Error", "Invalid license key")
                return
        except Exception as e:
            messagebox.showerror("Error", f"License validation failed: {str(e)}")
            return

        # Get app information
        try:
            main_data = self.keyauth_app.var("Main")
            if not main_data:
                messagebox.showerror("Error", "Failed to get app data")
                return

            apps_data = json.loads(main_data)
            app_info = None

            for app in apps_data["apps"]:
                if license_key.startswith(app["license_prefix"]):
                    app_info = app
                    break

            if not app_info:
                messagebox.showerror("Error", "No matching app found for this license key")
                return

            self.app_info = app_info
            self.log_message(f"Found app: {app_info['app_name']} (ID: {app_info['app_id']})")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get app information: {str(e)}")
            return

        # Start download in separate thread
        self.download_btn.configure(state='disabled')
        self.progress_bar.set(0)
        threading.Thread(target=self._download_thread, daemon=True).start()

    def _download_thread(self):
        """Download files thread (admin mode)"""
        try:
            success = self._download_all_files(self.app_info)

            if success:
                self.root.after(0, lambda: self.progress_var.set("✅ Download completed!"))
                self.root.after(0, lambda: self.log_message("🎉 All downloads completed!"))
            else:
                self.root.after(0, lambda: self.progress_var.set("❌ Download failed"))
                self.root.after(0, lambda: self.log_message("❌ Some downloads failed"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Download error: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.download_btn.configure(state='normal'))

    def browse_steam_path(self):
        """Browse for Steam installation path"""
        path = filedialog.askdirectory(title="Select Steam Installation Directory")
        if path and os.path.exists(os.path.join(path, "steam.exe")):
            self.steam_path = path
            self.config_manager.set("steam_path", path)
            self.steam_path_var.set(path)
            self.log_message(f"Steam path set to: {path}")
        elif path:
            messagebox.showerror("Error", "Invalid Steam directory. steam.exe not found.")

    def reset_all_data(self):
        """Reset all application data"""
        if messagebox.askyesno("Confirm Reset", "This will reset ALL application data including license history and settings. Continue?"):
            try:
                self.config_manager.reset_all_data()
                self.steam_path = ""
                if hasattr(self, 'steam_path_var'):
                    self.steam_path_var.set("Not configured")
                self.log_message("✅ All data reset successfully")
                messagebox.showinfo("Success", "All data has been reset")
            except Exception as e:
                self.log_message(f"❌ Error resetting data: {str(e)}")
                messagebox.showerror("Error", f"Failed to reset data: {str(e)}")

    def reset_stplug_folder(self):
        """Reset/delete stplug-in folder"""
        if not self.steam_path:
            messagebox.showerror("Error", "Steam path not set")
            return

        stplug_path = os.path.join(self.steam_path, "config", "stplug-in")

        if messagebox.askyesno("Confirm Reset", f"This will delete the stplug-in folder:\n{stplug_path}\n\nContinue?"):
            try:
                if os.path.exists(stplug_path):
                    shutil.rmtree(stplug_path)
                    self.log_message(f"✅ Deleted stplug-in folder: {stplug_path}")
                    messagebox.showinfo("Success", "stplug-in folder deleted successfully")
                else:
                    self.log_message("ℹ️ stplug-in folder does not exist")
                    messagebox.showinfo("Info", "stplug-in folder does not exist")
            except Exception as e:
                self.log_message(f"❌ Error deleting stplug-in folder: {str(e)}")
                messagebox.showerror("Error", f"Failed to delete folder: {str(e)}")

    def delete_all_files(self):
        """Delete all hid.dll and stplug-in folder"""
        if not self.steam_path:
            messagebox.showerror("Error", "Steam path not set")
            return

        hid_path = os.path.join(self.steam_path, "hid.dll")
        stplug_path = os.path.join(self.steam_path, "config", "stplug-in")

        if messagebox.askyesno("Confirm Deletion", f"This will delete:\n- {hid_path}\n- {stplug_path}\n\nContinue?"):
            try:
                deleted_count = 0

                # Delete hid.dll
                if os.path.exists(hid_path):
                    os.remove(hid_path)
                    self.log_message(f"✅ Deleted: {hid_path}")
                    deleted_count += 1

                # Delete stplug-in folder
                if os.path.exists(stplug_path):
                    shutil.rmtree(stplug_path)
                    self.log_message(f"✅ Deleted: {stplug_path}")
                    deleted_count += 1

                if deleted_count > 0:
                    messagebox.showinfo("Success", f"Deleted {deleted_count} item(s) successfully")
                else:
                    messagebox.showinfo("Info", "No files found to delete")

            except Exception as e:
                self.log_message(f"❌ Error deleting files: {str(e)}")
                messagebox.showerror("Error", f"Failed to delete files: {str(e)}")

    def view_license_history(self):
        """View license key history with modern design"""
        history = self.config_manager.config.get("license_key_history", [])

        if not history:
            messagebox.showinfo("License History", "No license key history found")
            return

        # Create modern history window
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("License Key History")
        history_window.geometry("900x500")

        # Make window modal
        history_window.transient(self.root)
        history_window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(history_window, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Header
        header_frame = ctk.CTkFrame(main_frame, corner_radius=15, height=80)
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = ctk.CTkLabel(header_frame, text="License Key History",
                                  font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(pady=25)

        # History content
        history_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        history_frame.pack(fill="both", expand=True, pady=(0, 20))

        # Create scrollable frame for history items
        scrollable_frame = ctk.CTkScrollableFrame(history_frame, corner_radius=0)
        scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Add history items
        for i, entry in enumerate(history):
            item_frame = ctk.CTkFrame(scrollable_frame, corner_radius=10)
            item_frame.pack(fill="x", pady=(0, 10))

            content_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
            content_frame.pack(fill="x", padx=20, pady=15)
            content_frame.grid_columnconfigure(1, weight=1)

            # Status indicator
            status = "✅" if entry.get("success", False) else "❌"
            status_label = ctk.CTkLabel(content_frame, text=status, font=ctk.CTkFont(size=16))
            status_label.grid(row=0, column=0, rowspan=2, padx=(0, 15), sticky="w")

            # App name
            app_label = ctk.CTkLabel(content_frame, text=entry.get("app_name", "Unknown"),
                                    font=ctk.CTkFont(size=14, weight="bold"))
            app_label.grid(row=0, column=1, sticky="w")

            # License key (truncated)
            key_display = entry.get("key", "")
            if len(key_display) > 30:
                key_display = key_display[:27] + "..."

            key_label = ctk.CTkLabel(content_frame, text=f"Key: {key_display}",
                                    font=ctk.CTkFont(size=12),
                                    text_color=("gray60", "gray40"))
            key_label.grid(row=1, column=1, sticky="w")

            # Date
            date_str = entry.get("timestamp", "Unknown")
            if "T" in date_str:
                try:
                    dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    date_str = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    date_str = date_str.split("T")[0]

            date_label = ctk.CTkLabel(content_frame, text=date_str,
                                     font=ctk.CTkFont(size=12),
                                     text_color=("gray60", "gray40"))
            date_label.grid(row=0, column=2, rowspan=2, padx=(15, 0), sticky="e")

        # Button frame
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x")

        # Clear history button
        def clear_history():
            if messagebox.askyesno("Confirm", "Clear all license history?"):
                self.config_manager.config["license_key_history"] = []
                self.config_manager.save_config()
                history_window.destroy()
                self.log_message("License history cleared")

        ctk.CTkButton(button_frame, text="Clear History", command=clear_history,
                     fg_color=("red", "darkred"), height=35).pack(side="left")

        # Close button
        ctk.CTkButton(button_frame, text="Close", command=history_window.destroy,
                     height=35).pack(side="right")

    def change_admin_password(self):
        """Change admin password"""
        current_password = simpledialog.askstring("Change Password", "Enter current admin password:", show='*')
        if not current_password or not self.password_manager.verify_password(current_password):
            messagebox.showerror("Error", "Incorrect current password")
            return

        new_password = simpledialog.askstring("Change Password", "Enter new admin password:", show='*')
        if not new_password:
            return

        confirm_password = simpledialog.askstring("Change Password", "Confirm new admin password:", show='*')
        if new_password != confirm_password:
            messagebox.showerror("Error", "Passwords do not match")
            return

        if len(new_password) < 4:
            messagebox.showerror("Error", "Password must be at least 4 characters long")
            return

        try:
            self.password_manager.change_password(new_password)
            self.log_message("Admin password changed successfully")
            messagebox.showinfo("Success", "Admin password changed successfully")
        except Exception as e:
            self.log_message(f"❌ Error changing password: {str(e)}")
            messagebox.showerror("Error", f"Failed to change password: {str(e)}")

    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main application entry point"""
    app = ModernSteamToolsGUI()

    # Save configuration on exit
    def on_closing():
        app.config_manager.save_config()
        app.root.destroy()

    app.root.protocol("WM_DELETE_WINDOW", on_closing)
    app.run()

if __name__ == "__main__":
    main()
